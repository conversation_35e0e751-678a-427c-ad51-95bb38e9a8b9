package request

import (
	"fmt"
	"io"
	"net/http"
	"net/url"
	"sync"
	"time"
)

var (
	// Shared HTTP client with optimized settings
	httpClient *http.Client
	clientOnce sync.Once
)

// getOptimizedClient returns a singleton HTTP client with optimized settings
func getOptimizedClient() *http.Client {
	clientOnce.Do(func() {
		transport := &http.Transport{
			MaxIdleConns:        100,
			MaxIdleConnsPerHost: 10,
			IdleConnTimeout:     90 * time.Second,
			DisableCompression:  false,
		}

		httpClient = &http.Client{
			Transport: transport,
			Timeout:   30 * time.Second,
		}
	})
	return httpClient
}

// MakeRequest is the original function for backward compatibility
func MakeRequest(bearerToken string, endpoint string, queryParameters map[string]string, responseWriter io.Writer) error {
	jiraBaseURL := "https://jerry.dieboldnixdorf.com/rest/"

	// Parse the URL and add query parameters
	parsedURL, parseError := url.Parse(jiraBaseURL + endpoint)
	if parseError != nil {
		fmt.Println("Error parsing URL:", parseError)
		return parseError
	}

	// Add query parameters
	urlQuery := parsedURL.Query()
	for parameterKey, parameterValue := range queryParameters {
		urlQuery.Add(parameterKey, parameterValue)
	}
	parsedURL.RawQuery = urlQuery.Encode()

	// Create a new request
	req, err := http.NewRequest("GET", parsedURL.String(), nil)
	if err != nil {
		fmt.Println("Error creating request:", err)
		return err
	}

	// Add the Bearer token to the Authorization header
	req.Header.Add("Authorization", "Bearer "+bearerToken)

	// Use the optimized client
	client := getOptimizedClient()
	resp, err := client.Do(req)
	if err != nil {
		fmt.Println("Error making request:", err)
		return err
	}
	defer resp.Body.Close()

	// Write response to the writer
	_, err = io.Copy(responseWriter, resp.Body)
	if err != nil {
		fmt.Println("Error writing response:", err)
		return err
	}

	return nil
}

// MakeRequestOptimized returns response data directly as bytes for better performance
func MakeRequestOptimized(bearerToken string, endpoint string, queryParameters map[string]string) ([]byte, error) {
	jiraBaseURL := "https://jerry.dieboldnixdorf.com/rest/"

	// Parse the URL and add query parameters
	parsedURL, parseError := url.Parse(jiraBaseURL + endpoint)
	if parseError != nil {
		return nil, fmt.Errorf("error parsing URL: %w", parseError)
	}

	// Add query parameters
	urlQuery := parsedURL.Query()
	for parameterKey, parameterValue := range queryParameters {
		urlQuery.Add(parameterKey, parameterValue)
	}
	parsedURL.RawQuery = urlQuery.Encode()

	// Create a new request
	req, err := http.NewRequest("GET", parsedURL.String(), nil)
	if err != nil {
		return nil, fmt.Errorf("error creating request: %w", err)
	}

	// Add the Bearer token to the Authorization header
	req.Header.Add("Authorization", "Bearer "+bearerToken)

	// Use the optimized client
	client := getOptimizedClient()
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("error making request: %w", err)
	}
	defer resp.Body.Close()

	// Read response body directly into bytes
	responseData, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("error reading response: %w", err)
	}

	return responseData, nil
}
